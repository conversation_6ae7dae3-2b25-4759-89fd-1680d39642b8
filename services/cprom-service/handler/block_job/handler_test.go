package block_job

/*
   go test -v -covermode=set -coverprofile=detail.out ./
   go tool cover -html=detail.out
*/

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	"gotest.tools/assert"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/configuration"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
	"icode.baidu.com/baidu/cprom/cloud-stack/services/cprom-service/handler"
)

var m cpromv1.BlockJob
var c *gin.Context
var nrApi *BlockJobAPI
var blockJobId string

func init() {
	nrApi = &BlockJobAPI{}
	nrApi.config = &configuration.ServiceConfig{}
	c, _ = gin.CreateTestContext(httptest.NewRecorder())
	c.Keys = map[string]interface{}{}
	c.Request = new(http.Request)
	c.Request.URL = new(url.URL)
	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
}

func TestCreateBlockJobHandler(t *testing.T) {
	blockJobBody := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectQuery("SELECT (.+)").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account_id", "block_job_id", "block_job_name",
			"block_windows", "block_periods", "block_rule_list_str", "block_rule_list", "comment", "operator", "update_time"}))
	mock.ExpectCommit()

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")

	// mock一个HTTP请求
	req := httptest.NewRequest("POST", "/", blockJobBody) // 请求参数
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	nrApi.Create(c)
}

func TestCreateBlockJobHandlerV1(t *testing.T) {
	blockJobBody := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectQuery("SELECT (.+)").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account_id", "block_job_id", "block_job_name",
			"block_windows", "block_periods", "block_rule_list_str", "block_rule_list", "comment", "operator", "update_time"}))
	mock.ExpectCommit()

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")

	// mock一个HTTP请求
	req := httptest.NewRequest("POST", "/", blockJobBody) // 请求参数
	req.Header.Set("Content-Type", "application/json")
	cookie := http.Cookie{Name: "bce-login-display-name", Value: "shiyiyu"}
	req.AddCookie(&cookie)
	c.Request = req
	nrApi.Create(c)

}

func TestCreateBlockJobHandlerV2(t *testing.T) {
	blockJobBody1 := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"repeatDays": [
        1,
        2
    ],
    "blockWindows": [
        {
            "startTimeStr": "10:00:00",
            "endTimeStr": "23:00:00"
        }
    ]
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	blockJobBody2 := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"repeatDays": [
        1,
        2
    ],
    "blockWindows": [
        {
            "startTimeStr": "10:00:00",
            "endTimeStr": "23:00:00"
        }
    ],
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectQuery("SELECT (.+)").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account_id", "block_job_id", "block_job_name",
			"block_windows", "block_periods", "block_rule_list_str", "block_rule_list", "comment", "operator", "update_time"}))
	mock.ExpectCommit()

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")

	// mock一个HTTP请求
	// error1
	req := httptest.NewRequest("POST", "/", blockJobBody1) // 请求参数
	req.Header.Set("Content-Type", "application/json")
	c.Request = req
	nrApi.Create(c)

	// error2
	req2 := httptest.NewRequest("POST", "/", blockJobBody2) // 请求参数
	req2.Header.Set("Content-Type", "application/json")
	c.Request = req2
	nrApi.Create(c)
}

func TestModifyBlockJobHandler(t *testing.T) {
	blockJobBody := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("UPDATE (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	// mock一个HTTP请求
	req := httptest.NewRequest(
		"PUT",        // 请求方法
		"/",          // 请求URL
		blockJobBody, // 请求参数
	)

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
	c.Set("blockJobID", "blockJobID")

	c.Request = req
	nrApi.Update(c)
}

func TestModifyBlockJobHandlerV1(t *testing.T) {
	blockJobBody := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("UPDATE (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	// mock一个HTTP请求
	req := httptest.NewRequest(
		"PUT",        // 请求方法
		"/",          // 请求URL
		blockJobBody, // 请求参数
	)

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
	c.Set("blockJobID", "blockJobID")
	req.Header.Set("Content-Type", "application/json")
	cookie := http.Cookie{Name: "bce-login-display-name", Value: "shiyiyu"}
	req.AddCookie(&cookie)
	c.Request = req
	nrApi.Update(c)
}

func TestModifyBlockJobHandlerV2(t *testing.T) {
	blockJobBody1 := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"repeatDays": [
        1,
        2
    ],
    "blockWindows": [
        {
            "startTimeStr": "10:00:00",
            "endTimeStr": "23:00:00"
        }
    ]
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	blockJobBody2 := strings.NewReader(`{
	"blockJobName": "blockJob_shi",
	"repeatDays": [
        1,
        2
    ],
    "blockWindows": [
        {
            "startTimeStr": "10:00:00",
            "endTimeStr": "23:00:00"
        }
    ],
	"blockPeriods": [{
		"startTime": **********,
		"duration": "1h",
		"endTime": **********
	}],	
	"blockRuleList": [
		{
			"tagKey": "alertname",
			"matchOperator": "=",
			"tagValue": "alertingRule_shi_0424"
		},
		{
			"tagKey": "idc",
			"matchOperator": "!=",
			"tagValue": "bj"
		}
	],
	"comment": "blockJob_shi_0425_testAlert_comment",
	"operator": "shiyiyu"
}`)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec("UPDATE (.+)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	// mock一个HTTP请求

	// error1
	req := httptest.NewRequest(
		"PUT",         // 请求方法
		"/",           // 请求URL
		blockJobBody1, // 请求参数
	)
	req.Header.Set("Content-Type", "application/json")
	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
	c.Set("blockJobID", "blockJobID")

	c.Request = req
	nrApi.Update(c)

	// error2
	req2 := httptest.NewRequest(
		"PUT",         // 请求方法
		"/",           // 请求URL
		blockJobBody2, // 请求参数
	)
	req2.Header.Set("Content-Type", "application/json")
	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
	c.Set("blockJobID", "blockJobID")

	c.Request = req2
	nrApi.Update(c)
}

func TestDeleteBlockJobHandler(t *testing.T) {
	blockJobBody := strings.NewReader(``)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `notify_rules`  WHERE (notify_rule_id = ?) AND (account_id = ?)")).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	// mock一个HTTP请求
	req := httptest.NewRequest(
		"DELETE",     // 请求方法
		"/",          // 请求URL
		blockJobBody, // 请求参数
	)

	c.Set(handler.RequestID, "6bdd602e-06b4-42da-9784-57ebbd4121ab")
	c.Set("accountID", "6bdd602e-06b4-42da-9784")
	c.Set("blockJobID", "blockJobID")

	c.Request = req
	nrApi.Delete(c)
}

func TestQueryNotifyRuleHandler(t *testing.T) {
	blockJobBody := strings.NewReader(``)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// mock执行指定SQL语句时的返回结果
	//mock.ExpectBegin()  // ExpectQuery前后不能加ExpectBegin、ExpectCommit！！
	mock.ExpectQuery("SELECT (.+)").
		WillReturnRows(sqlmock.NewRows([]string{"id", "account_id", "block_job_id", "block_job_name",
			"block_windows", "block_periods", "block_rule_list_str", "block_rule_list", "comment", "operator", "update_time"}).
			AddRow(1213, "sdfa", "sadf", "sfas", "sfs", "sdfa", "sfas", "sfas", "sfas", "sfas", time.Now()))

	// mock一个HTTP请求
	req := httptest.NewRequest(
		"GET",        // 请求方法
		"/",          // 请求URL
		blockJobBody, // 请求参数
	)

	c.Set("blockJobID", "blockJobID")

	c.Request = req
	nrApi.Get(c)
}

func TestBatchQueryNotifyRuleHandler(t *testing.T) {
	blockJobBody := strings.NewReader(``)

	// mock一个*sql.DB对象，不需要连接真实的数据库
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	mysql.Conn, err = gorm.Open("mysql", db)

	// 增加返回值
	rows := sqlmock.NewRows([]string{"id", "account_id", "block_job_id", "block_job_name", "block_windows", "block_periods", "block_rule_list_str", "block_rule_list", "comment", "operator", "update_time"}).
		AddRow(1, "6bdd602e-06b4-42da-9784", "block-sgsa2314", "blockJob_shi", "",
			"[{\"startTime\":**********,\"duration\":\"1h\",\"endTime\":**********}]", "block_rule_list_str",
			"[{\"tagKey\":\"alertname\",\"matchOperator\":\"=\",\"tagValue\":\"alertingRule_shi_0424\"},{\"tagKey\":\"idc\",\"matchOperator\":\"!=\",\"tagValue\":\"bj\"}]",
			"comment", "operator", time.Now().Unix())

	// mock执行指定SQL语句时的返回结果
	sqlCmd := "SELECT * FROM `block_jobs` WHERE (account_id = ?) AND (block_job_name like \"%%\" or block_rule_list_str like \"%%\") ORDER BY update_time desc"
	mock.ExpectQuery(regexp.QuoteMeta(sqlCmd)).
		WithArgs("").WithArgs("").WithArgs("6bdd602e-06b4-42da-9784").WillReturnRows(rows)

	// mock一个HTTP请求
	req := httptest.NewRequest(
		"GET",        // 请求方法
		"/",          // 请求URL
		blockJobBody, // 请求参数
	)

	c.Set("pageSize", 2)
	c.Set("pageNo", 1)

	c.Request = req
	nrApi.List(c)
}

func TestConvertBlockRuleList2Str(t *testing.T) {
	blockRule := cpromv1.BlockRule{
		TagKey:        "tagKey1",
		MatchOperator: "=",
		TagValue:      "tagValue1",
	}
	blockRule2 := cpromv1.BlockRule{
		TagKey:        "tagKey2",
		MatchOperator: "!=",
		TagValue:      "tagValue2",
	}
	blockRuleList := []cpromv1.BlockRule{blockRule, blockRule2}

	blockRuleListStr := convertBlockRuleList2Str(blockRuleList)
	assert.Equal(t, blockRuleListStr, "tagKey1=\"tagValue1\",tagKey2!=\"tagValue2\"")
}

func TestGetBlockStatus(t *testing.T) {
	nowTimestamp := time.Now().Unix()

	// 测试running
	blockPeriod := cpromv1.PeriodSpan{
		StartTime: nowTimestamp - 100,
		EndTime:   nowTimestamp + 100,
	}
	blockPeriodList := []cpromv1.PeriodSpan{blockPeriod}
	blockJob := cpromv1.BlockJob{
		BlockPeriods: blockPeriodList,
	}
	blockStatus := getBlockStatus(&blockJob)
	assert.Equal(t, cpromv1.BLOCK_STATUS_RUNNING, blockStatus)

	// 测试pending
	blockPeriod = cpromv1.PeriodSpan{
		StartTime: nowTimestamp + 100,
		EndTime:   nowTimestamp + 200,
	}
	blockPeriodList = []cpromv1.PeriodSpan{blockPeriod}
	blockJob = cpromv1.BlockJob{
		BlockPeriods: blockPeriodList,
	}
	blockStatus = getBlockStatus(&blockJob)
	assert.Equal(t, cpromv1.BLOCK_STATUS_PENDING, blockStatus)

	// 测试end
	blockPeriod = cpromv1.PeriodSpan{
		StartTime: nowTimestamp - 200,
		EndTime:   nowTimestamp - 100,
	}
	blockPeriodList = []cpromv1.PeriodSpan{blockPeriod}
	blockJob = cpromv1.BlockJob{
		BlockPeriods: blockPeriodList,
	}
	blockStatus = getBlockStatus(&blockJob)
	assert.Equal(t, cpromv1.BLOCK_STATUS_END, blockStatus)

}

func TestGetBlockStatusForWindow(t *testing.T) {
	//nowTimestamp := time.Now().Unix()

	// 测试running
	repeatDay := []int{1, 2, 3, 4, 5, 6, 7}
	blockWindow := cpromv1.TimeSpanV2{
		StartTime: "00:00:00",
		EndTime:   "23:59:59",
	}
	blockWindowList := []cpromv1.TimeSpanV2{blockWindow}
	blockJob := cpromv1.BlockJob{
		BlockWindows: blockWindowList,
		RepeatDays:   repeatDay,
	}
	blockStatus := getBlockStatus(&blockJob)
	assert.Equal(t, cpromv1.BLOCK_STATUS_RUNNING, blockStatus)

}

func TestValidateBlockJob(t *testing.T) {
	// case1
	blockRule := cpromv1.BlockRule{
		TagKey:        "abc",
		MatchOperator: "=",
		TagValue:      "",
	}
	blockRuleList := []cpromv1.BlockRule{blockRule}

	repeatDay := []int{1, 2, 3, 4, 5, 6, 7}
	blockWindow := cpromv1.TimeSpanV2{
		StartTime: "00:00:00",
		EndTime:   "23:59:59",
	}
	blockWindowList := []cpromv1.TimeSpanV2{blockWindow}
	blockJob := cpromv1.BlockJob{
		BlockRuleList: blockRuleList,
		BlockWindows:  blockWindowList,
		RepeatDays:    repeatDay,
	}
	validateBlockJob(c, blockJob)

}

func TestValidateBlockRuleList(t *testing.T) {
	// case1
	blockRule := cpromv1.BlockRule{
		TagKey:        "abc",
		MatchOperator: "=",
		TagValue:      "",
	}
	blockRuleList := []cpromv1.BlockRule{blockRule}
	validateBlockRuleList(blockRuleList)

	// case2
	blockRule1 := cpromv1.BlockRule{
		TagKey:        "",
		MatchOperator: "=",
		TagValue:      "123",
	}
	blockRuleList1 := []cpromv1.BlockRule{blockRule1}
	validateBlockRuleList(blockRuleList1)

	// case3
	blockRule2 := cpromv1.BlockRule{
		TagKey:        "_abv",
		MatchOperator: "=",
		TagValue:      "123",
	}
	blockRuleList2 := []cpromv1.BlockRule{blockRule2}
	validateBlockRuleList(blockRuleList2)

	//nowTimestamp := time.Now().Unix()

	// 测试running
	repeatDay := []int{1, 2, 3, 4, 5, 6, 7}
	blockWindow := cpromv1.TimeSpanV2{
		StartTime: "00:00:00",
		EndTime:   "23:59:59",
	}
	blockWindowList := []cpromv1.TimeSpanV2{blockWindow}
	blockJob := cpromv1.BlockJob{
		BlockWindows: blockWindowList,
		RepeatDays:   repeatDay,
	}
	blockStatus := getBlockStatus(&blockJob)
	assert.Equal(t, cpromv1.BLOCK_STATUS_RUNNING, blockStatus)

}
