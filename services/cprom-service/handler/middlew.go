package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/VictoriaMetrics/metricsql"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	"github.com/pkg/errors"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/ccr/v1alpha1"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	monitoringv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/monitoring/v1"
	vmv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/errorcodev2"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/logger"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/middleware"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/alerting_rule"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/block_job"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/recording_rule"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage"
)

const (
	RequestID   = "x-bce-request-id"
	EnableCprom = "EnableCProm"
)

func RequiredWithID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 RequestID
		reqID := c.GetHeader(RequestID)
		if reqID == "" {
			reqID = logger.GetUUID()
		}

		c.Set(RequestID, reqID)

		c.Header("X-Bce-Request-Id", reqID)
	}
}

func NewContext(c *gin.Context) context.Context {
	reqID := c.Value(RequestID).(string)

	return context.WithValue(context.Background(), logger.RequestID, reqID)
}

// RequiredWithAuth 认证鉴权中间件该 middlew 必须放在 RequiredWithID 之后
func RequiredWithAuth(mw middleware.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := NewContext(c)
		// 调用 iam 接口做认证
		userInfo, err := mw.ParseRequester(ctx, c.Request)
		if err != nil {
			message := fmt.Sprintf("get error when parse requester: %v", err)
			logger.Errorf(ctx, message)
			c.JSON(http.StatusInternalServerError, gin.H{"message": message})
			c.Abort()
			return
		}
		if userInfo == nil {
			logger.Errorf(ctx, "userInfo is nil")
			c.JSON(http.StatusUnauthorized, gin.H{"message": "userInfo is nil"})
			c.Abort()
			return
		}
		logger.Infof(ctx, "accountID: %v start to visit: %v, method: %v", userInfo.AccountID, c.Request.URL, c.Request.Method)

		// 调用iam 接口进行鉴权，由于涉及标签鉴权，需要提前获取userID，所以将认证和鉴权分开。

		authClient := NewAuthClient(mw)
		if userInfo.UserID == "" {
			userInfo.UserID = DefaultOwner
		}

		result := authClient.Authorization(c, ctx, userInfo.AccountID, userInfo.UserID)
		if !result {
			c.Abort()
			return
		}

		c.Set("userID", userInfo.UserID)

		// 方便后端模块访问，后端统一用一个ak、sk访问，但会在query param中带上accountID
		accountID := c.Query("accountID") // 从query param中取accountID
		if accountID != "" {
			c.Set("accountIDBackend", accountID)
			logger.Infof(ctx, "get accountIDBackend from param of path. accountIDBackend: %v, path: %v, method: %v",
				accountID, c.Request.URL, c.Request.Method)
		}

		c.Set("accountID", userInfo.AccountID)

		c.Set("ruleID", userInfo.RoleID)
		c.Set("assumerID", userInfo.AssumerID)
		c.Set("roleOwnerID", userInfo.RoleOwnerID)

	}
}

// ValidateInstance 校验 instance owner 该 middlew 必须放在 RequiredWithAuth 之后
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数
func ValidateInstance(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx        = NewContext(c)
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.Query("instanceID") // from query param
			res        = NewResult()
			errRes     = errorcodev2.NewResponse()
		)

		requestID := logger.GetRequestID(ctx)
		if instanceID == "" {
			instanceID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		logger.Infof(ctx, "query db TAccountManage with instanceID: %v", instanceID)
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			logger.Errorf(ctx, "query db TAccountManage with instanceID: %v err: %v", instanceID, err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(requestID))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			var m cpromv1.MonitorInstance
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}
			logger.Infof(ctx, "namespace is %v", insInfo.FNamespace)
			nn := types.NamespacedName{Name: insInfo.FNamespace}
			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					errRes.Message = fmt.Sprintf("not found instance: %v", insInfo.FNamespace)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check instance: %v, err: %v", insInfo.FNamespace, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return

			}
			// 2023.9.21 多租户实例需要检查多租户对应的cncnetwork是否已调和创建出来，否则实例状态应为creating
			// 避免cncnetwork未就绪时创建cprom agent报错
			nn = types.NamespacedName{
				Namespace: insInfo.FNamespace,
				Name:      insInfo.FNamespace + "-" + insInfo.FBceAccount,
			}
			var nc ccrv1alpha1.CNCNetwork
			err = k8sClient.Get(ctx, nn, &nc)
			if err != nil {
				if apierrors.IsNotFound(err) {
					m.Status.Ready = false
					m.Status.Phase = cpromv1.MonitorInstancePhaseCreating
					m.Status.Message = "multi tenant cncnetwork creating"
				}
			}

			c.Set("instanceID", instanceID)
			c.Set("newInstanceID", instanceID)
			c.Set("newNameSpace", insInfo.FNamespace)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("instance").(*cpromv1.MonitorInstance) 获取
			c.Set("instance", &m)
		} else {
			nn := types.NamespacedName{Name: instanceID}

			var m cpromv1.MonitorInstance

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					errRes.Message = fmt.Sprintf("not found instance: %v", instanceID)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check instance: %v, err: %v", instanceID, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return

			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}

			c.Set("instanceID", instanceID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("instance").(*cpromv1.MonitorInstance) 获取
			c.Set("instance", &m)
		}
	}
}

// ValidateInstance 校验 instance owner 该 middlew 必须放在 RequiredWithAuth 之后
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数，OpenApi V2版本规范
func ValidateInstanceV2(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx        = NewContext(c)
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.Query("instanceId") // from query param
			res        = errorcodev2.NewResponse()
		)

		requestID := logger.GetRequestID(ctx)

		if instanceID == "" {
			instanceID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		logger.Infof(ctx, "query db TAccountManage with instanceID: %v", instanceID)
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			logger.Errorf(ctx, "query db TAccountManage with instanceID: %v err: %v", instanceID, err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(requestID))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			var m cpromv1.MonitorInstance
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}
			logger.Infof(ctx, "namespace is %v", insInfo.FNamespace)
			nn := types.NamespacedName{Name: insInfo.FNamespace}
			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found instance: %v", insInfo.FNamespace)
					logger.Errorf(ctx, res.Message)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check instance: %v, err: %v", insInfo.FNamespace, err)
				logger.Errorf(ctx, res.Message)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(requestID))
				c.Abort()
				return

			}
			// 2023.9.21 多租户实例需要检查多租户对应的cncnetwork是否已调和创建出来，否则实例状态应为creating
			// 避免cncnetwork未就绪时创建cprom agent报错
			nn = types.NamespacedName{
				Namespace: insInfo.FNamespace,
				Name:      insInfo.FNamespace + "-" + insInfo.FBceAccount,
			}
			var nc ccrv1alpha1.CNCNetwork
			err = k8sClient.Get(ctx, nn, &nc)
			if err != nil {
				if apierrors.IsNotFound(err) {
					m.Status.Ready = false
					m.Status.Phase = cpromv1.MonitorInstancePhaseCreating
					m.Status.Message = "multi tenant cncnetwork creating"
				}
			}

			c.Set("instanceId", instanceID)
			c.Set("newInstanceID", instanceID)
			c.Set("newNameSpace", insInfo.FNamespace)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("instance").(*cpromv1.MonitorInstance) 获取
			c.Set("instance", &m)
		} else {
			nn := types.NamespacedName{Name: instanceID}

			var m cpromv1.MonitorInstance

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found instance: %v", instanceID)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check instance: %v, err: %v", instanceID, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				c.Abort()
				return

			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}

			c.Set("instanceId", instanceID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("instance").(*cpromv1.MonitorInstance) 获取
			c.Set("instance", &m)
		}
	}
}

// ValidateAgent 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数
func ValidateAgent(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx        = NewContext(c)
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.MustGet("instanceID").(string)
			agentID    = c.Query("agentID")
			res        = NewResult()
		)

		if agentID == "" {
			agentID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn := types.NamespacedName{Name: agentID, Namespace: insInfo.FNamespace}

			var m cpromv1.MonitorAgent

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found agent: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check agent: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			c.Set("agentID", agentID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("agent").(*cpromv1.MonitorAgentl) 获取
			c.Set("agent", &m)
			c.Set("newNameSpace", insInfo.FNamespace)
		} else {
			nn := types.NamespacedName{Name: agentID, Namespace: instanceID}

			var m cpromv1.MonitorAgent

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found agent: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check agent: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("agentID", agentID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("agent").(*cpromv1.MonitorAgentl) 获取
			c.Set("agent", &m)
		}
	}
}

// ValidateAgent 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数，OpenApi V2版本规范
func ValidateAgentV2(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx        = NewContext(c)
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.MustGet("instanceId").(string)
			agentID    = c.Query("agentId")
			res        = errorcodev2.NewResponse()
		)
		requestID := logger.GetRequestID(ctx)
		if agentID == "" {
			agentID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, instanceID))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}
			nn := types.NamespacedName{Name: agentID, Namespace: insInfo.FNamespace}

			var m cpromv1.MonitorAgent

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, nn.Name))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check agent: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				c.Abort()
				return
			}

			c.Set("agentId", agentID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("agent").(*cpromv1.MonitorAgentl) 获取
			c.Set("agent", &m)
			c.Set("newNameSpace", insInfo.FNamespace)
		} else {
			nn := types.NamespacedName{Name: agentID, Namespace: instanceID}

			var m cpromv1.MonitorAgent

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, nn.Name))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check agent: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				c.Abort()
				return
			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
				return
			}

			c.Set("agentId", agentID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("agent").(*cpromv1.MonitorAgentl) 获取
			c.Set("agent", &m)
		}
	}
}

// ValidateScrapeJob 校验 instance owner 该 middlew 必须放在 ValidateAgent 之后
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数
func ValidateScrapeJob(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx         = NewContext(c)
			accountID   = c.MustGet("accountID").(string)
			instanceID  = c.MustGet("instanceID").(string)
			agentID     = c.MustGet("agentID").(string)
			scrapeJobID = c.Query("ID")
			res         = NewResult()
		)

		if scrapeJobID == "" {
			scrapeJobID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn := types.NamespacedName{Name: scrapeJobID, Namespace: insInfo.FNamespace}

			var m cpromv1.ScrapeJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found scrapeJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check scrapeJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}
			c.Set("scrapeJobID", scrapeJobID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("scrapeJob", &m)
			c.Set("newNameSpace", insInfo.FNamespace)

		} else {
			nn := types.NamespacedName{Name: scrapeJobID, Namespace: instanceID}

			var m cpromv1.ScrapeJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found scrapeJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check scrapeJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			if m.Spec.AgentID != agentID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("scrapeJobID", scrapeJobID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("scrapeJob", &m)
		}
	}
}

// ValidateScrapeJob 校验 instance owner 该 middlew 必须放在 ValidateAgent 之后
func ValidateScrapeJobV2(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx         = NewContext(c)
			accountID   = c.MustGet("accountID").(string)
			instanceID  = c.MustGet("instanceId").(string)
			agentID     = c.MustGet("agentId").(string)
			scrapeJobID = c.Query("ID")
			res         = NewResult()
		)

		if scrapeJobID == "" {
			scrapeJobID = c.Param("ID")
		}

		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn := types.NamespacedName{Name: scrapeJobID, Namespace: insInfo.FNamespace}

			var m cpromv1.ScrapeJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found scrapeJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check scrapeJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}
			logger.Infof(ctx, "get scrapeJob: %v", scrapeJobID)
			c.Set("scrapeJobID", scrapeJobID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("scrapeJob", &m)
			c.Set("newNameSpace", insInfo.FNamespace)

		} else {
			nn := types.NamespacedName{Name: scrapeJobID, Namespace: instanceID}

			var m cpromv1.ScrapeJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found scrapeJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check scrapeJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			if m.Spec.AgentID != agentID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("scrapeJobID", scrapeJobID)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("scrapeJob", &m)
		}
	}
}

// ValidateServiceMonitor 校验 instance owner 该 middlew 必须放在 ValidateAgent 之后
func ValidateServiceMonitor(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx                = NewContext(c)
			accountID          = c.MustGet("accountID").(string)
			instanceID         = c.MustGet("instanceId").(string)
			agentID            = c.MustGet("agentId").(string)
			serviceMonitorName = c.Query("ID")
			res                = errorcodev2.NewResponse()
		)

		if serviceMonitorName == "" {
			serviceMonitorName = c.Param("ID")
		}
		requestID := logger.GetRequestID(ctx)
		serviceMonitorName = serviceMonitorName + "-" + agentID

		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn := types.NamespacedName{Name: serviceMonitorName, Namespace: insInfo.FNamespace}

			var m monitoringv1.ServiceMonitor

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					shortnn := types.NamespacedName{Name: RemoveAgentID(serviceMonitorName), Namespace: insInfo.FNamespace}
					err = k8sClient.Get(ctx, shortnn, &m)

					if err == nil {
						c.Set("serviceMonitorName", serviceMonitorName)
						c.Set("serviceMonitor", &m)
						c.Set("newNameSpace", insInfo.FNamespace)
						logger.Infof(ctx, "get ServiceMonitor scrapeJob: %v", serviceMonitorName)
						return
					}
				}
				res.Message = fmt.Sprintf("check ServiceMonitor: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				c.Abort()
				return
			}
			logger.Infof(ctx, "get ServiceMonitor scrapeJob: %v", serviceMonitorName)
			c.Set("serviceMonitorName", serviceMonitorName)

			c.Set("serviceMonitor", &m)
			c.Set("newNameSpace", insInfo.FNamespace)

		} else {
			nn := types.NamespacedName{Name: serviceMonitorName, Namespace: instanceID}

			var m monitoringv1.ServiceMonitor

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					shortnn := types.NamespacedName{Name: RemoveAgentID(serviceMonitorName), Namespace: insInfo.FNamespace}
					err = k8sClient.Get(ctx, shortnn, &m)
					if err == nil {
						c.Set("serviceMonitorName", serviceMonitorName)
						c.Set("serviceMonitor", &m)
						logger.Infof(ctx, "get ServiceMonitor ServiceMonitor: %v", shortnn)
						return
					}
				}

				res.Message = fmt.Sprintf("check ServiceMonitor: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认是 accountID owner
			if m.Labels[cpromv1.BCEAccountIDLabel] != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			if m.Labels[cpromv1.AgentIDLabel] != agentID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("serviceMonitorName", serviceMonitorName)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("serviceMonitor", &m)
		}
	}
}

// ValidatePodMonitor 校验 instance owner 该 middlew 必须放在 ValidateAgent 之后
func ValidatePodMonitor(k8sClient client.Client, accountManage vm_manage.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx            = NewContext(c)
			accountID      = c.MustGet("accountID").(string)
			instanceID     = c.MustGet("instanceId").(string)
			agentID        = c.MustGet("agentId").(string)
			podMonitorName = c.Query("ID")
			res            = errorcodev2.NewResponse()
		)

		if podMonitorName == "" {
			podMonitorName = c.Param("ID")
		}
		requestID := logger.GetRequestID(ctx)
		podMonitorName = podMonitorName + "-" + agentID

		//insInfo := vm_manage.TAccountManage{}
		//insInfo := accountManage
		insInfo, err := accountManage.QueryByNewInstanceId(nil, instanceID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
				logger.Errorf(ctx, "namespace is %v,err : %v", instanceID, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
				c.Abort()
				return
			}
			//v1老实例不在数据库中查MIList
			if insInfo == nil {
				insInfo = &vm_manage.TAccountManage{}
			}
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn := types.NamespacedName{Name: podMonitorName, Namespace: insInfo.FNamespace}

			var m monitoringv1.PodMonitor

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found PodMonitor: %v, err: %v", nn, err)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}
				res.Message = fmt.Sprintf("check PodMonitor: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
				c.Abort()
				return
			}
			logger.Infof(ctx, "get PodMonitor scrapeJob: %v,namespace: %v", podMonitorName, insInfo.FNamespace)
			c.Set("podMonitorName", podMonitorName)

			c.Set("podMonitor", &m)
			c.Set("newNameSpace", insInfo.FNamespace)

		} else {
			nn := types.NamespacedName{Name: podMonitorName, Namespace: instanceID}

			var m monitoringv1.PodMonitor

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found PodMonitor: %v, err: %v", nn, err)
					c.JSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check PodMonitor: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认是 accountID owner
			if m.Labels[cpromv1.BCEAccountIDLabel] != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			if m.Labels[cpromv1.AgentIDLabel] != agentID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("podMonitorName", podMonitorName)
			// 设置监控实例给下一个 后续可以通过 c.MustGet("scrapeJob").(*cpromv1.ScrapeJob) 获取
			c.Set("podMonitor", &m)
		}
	}
}

func Pagination() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx      = NewContext(c)
			res      = NewResult()
			pageNo   = c.DefaultQuery("pageNo", "1")
			pageSize = c.DefaultQuery("pageSize", "10")
		)

		n, err := strconv.Atoi(pageNo)
		if err != nil {
			res.Message = fmt.Sprintf("pageNo must be int err: %v", err)
			res.Success = false

			logger.Errorf(ctx, res.Message)

			c.JSON(http.StatusBadRequest, res)
			c.Abort()
			return
		}
		s, err := strconv.Atoi(pageSize)
		if err != nil {
			res.Message = fmt.Sprintf("pageSize must be int err: %v", err)
			res.Success = false

			logger.Errorf(ctx, res.Message)

			c.JSON(http.StatusBadRequest, res)
			c.Abort()
			return
		}

		c.Set("pageNo", n)
		c.Set("pageSize", s)
	}
}

// ValidateNotifyRule 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = notifyRuleID 可以来自 path 参数，也可以来自 query 参数
func ValidateNotifyRule() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			notifyRuleID = c.Query("notifyRuleID")
		)

		if notifyRuleID == "" {
			notifyRuleID = c.Param("ID")
		}
		c.Set("notifyRuleID", notifyRuleID)
	}
	/*
		return func(c *gin.Context) {
			var (
				ctx          = NewContext(c)
				accountID    = c.MustGet("accountID").(string)
				notifyRuleID = c.Query("notifyRuleID")
				res          = NewResult()
			)

			if notifyRuleID == "" {
				notifyRuleID = c.Param("ID")
			}

			nn := types.NamespacedName{Name: notifyRuleID}

			var nr cpromv1.NotifyRule

			if err := k8sClient.Get(ctx, nn, &nr); err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found notifyRule: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check notifyRule: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}
			// 这里只确认 monitorInstance 是 accountID owner
			if nr.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("notifyRuleID", notifyRuleID)
			// 设置notifyRule给下一个middleware 后续可以通过 c.MustGet("notifyRule").(*cpromv1.NotifyRule) 获取
			c.Set("notifyRule", &nr)
		}
	*/
}

// ValidateBlockJob 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = notifyRuleID 可以来自 path 参数，也可以来自 query 参数
func ValidateBlockJob() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			blockJobID = c.Query("blockJobID")
			res        = NewResult()
		)

		if blockJobID == "" {
			blockJobID = c.Param("ID")
		}

		// 检查accountID下是否有该blockJob
		accountId, _ := c.MustGet("accountID").(string)
		_, err := block_job.QueryById(blockJobID, accountId)
		if err != nil {
			res.Message = fmt.Sprintf("not found blockJob: %v", blockJobID)
			c.JSON(http.StatusNotFound, res)
			c.Abort()
			return
		}
		c.Set("blockJobID", blockJobID)
	}
}

func ValidateXSS() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 定义常见的XSS攻击模式
		xssPatterns := []string{
			`<script`, `</script>`,
			`javascript:`, `vbscript:`, `data:`,
			`onerror=`, `onload=`, `onclick=`, `onmouseover=`, `onsubmit=`, `src=`,
			`on\w+=`, // 所有on事件处理器
			`eval\(`, `alert\(`, `prompt\(`, `confirm\(`,
			`document\.cookie`, `window\.location`,
			`expression\(`, `&#`, `\\x3C`,
			`<\w+\s+\w+=`, `<\w+>.*<\/\w+>`, // 通用标签模式
			`<img\s+`, `<\s*img\s+`, // 专门检测img标签
			`\bsrc\s*=\s*["']?\s*javascript:`, // 检测javascript:伪协议
			`\bsrc\s*=\s*["']?\s*vbscript:`,
			`\bsrc\s*=\s*["']?\s*data:`,
			`\bbackground\s*=\s*["']?\s*javascript:`, // 其他可能属性
		}

		// 检查单个值
		checkValue := func(value string) bool {
			if value == "" {
				return false
			}
			lowerValue := strings.ToLower(value)
			for _, pattern := range xssPatterns {
				if strings.Contains(lowerValue, pattern) {
					return true
				}
			}
			return false
		}

		// 检查Query参数
		queryParams := c.Request.URL.Query()
		logger.Infof(NewContext(c), "ValidateXSS query: %v", queryParams)
		for key, values := range queryParams {
			for _, value := range values {
				if checkValue(value) {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
						"message": fmt.Sprintf("检测到潜在XSS攻击风险(query参数:%s)", key),
					})
					return
				}
			}
		}

		// 检查Post表单参数
		if err := c.Request.ParseForm(); err == nil {
			logger.Infof(NewContext(c), "ValidateXSS form: %v", c.Request.PostForm)
			for key, values := range c.Request.PostForm {
				for _, value := range values {
					if checkValue(value) {
						c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
							"message": fmt.Sprintf("检测到潜在XSS攻击风险(form参数:%s)", key),
						})
						return
					}
				}
			}
		}

		// 检查Path参数
		logger.Infof(NewContext(c), "ValidateXSS path params: %v", c.Params)
		for _, param := range c.Params {
			if checkValue(param.Value) {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
					"message": fmt.Sprintf("检测到潜在XSS攻击风险(path参数:%s)", param.Key),
				})
				return
			}
		}

		bodyBytes, err := io.ReadAll(c.Request.Body)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"message": "读取请求体失败",
			})
			return
		}
		// Store it in context
		c.Set("requestBody", bodyBytes)

		// Reset the request body so it can be read again
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		bodyStr := string(bodyBytes)
		logger.Infof(NewContext(c), "ValidateXSS body: %v", bodyStr)
		if checkValue(bodyStr) {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"message": "检测到潜在XSS攻击风险(请求体)",
			})
			return
		}

	}
}

// ValidateBCMJob 校验 instance owner
// ID = instanceID 可以来自 path 参数，也可以来自 query 参数
func ValidateBCMJob(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx        = NewContext(c)
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.MustGet("instanceID").(string)
			bcmJobID   = c.Query("ID")
			res        = NewResult()
		)

		if bcmJobID == "" {
			bcmJobID = c.Param("ID")
		}

		var nn types.NamespacedName
		insInfo := vm_manage.TAccountManage{}
		err := insInfo.Get(nil, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("mysql connect instance err: %v", err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			c.Abort()
			return
		}
		if len(insInfo.FNewInstanceId) > 0 {
			// 这里只确认 monitorInstance 是 accountID owner
			if insInfo.FBceAccount != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}
			nn = types.NamespacedName{Name: bcmJobID, Namespace: insInfo.FNamespace}

			var m cpromv1.BCMJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found bcmJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check bcmJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			c.Set("bcmJobID", bcmJobID)
			// 后续可以通过 c.MustGet("bcmJob").(*cpromv1.BCMJob) 获取
			c.Set("bcmJob", &m)
			c.Set("newNameSpace", insInfo.FNamespace)
		} else {
			nn = types.NamespacedName{Name: bcmJobID, Namespace: instanceID}

			var m cpromv1.BCMJob

			err := k8sClient.Get(ctx, nn, &m)
			if err != nil {
				if apierrors.IsNotFound(err) {
					res.Message = fmt.Sprintf("not found bcmJob: %v", nn)
					c.JSON(http.StatusNotFound, res)
					c.Abort()
					return
				}

				res.Message = fmt.Sprintf("check bcmJob: %v, err: %v", nn, err)
				c.JSON(http.StatusInternalServerError, res)
				c.Abort()
				return
			}

			// 这里只确认 monitorInstance 是 accountID owner
			if m.Spec.AccountID != accountID {
				c.AbortWithStatusJSON(http.StatusNotFound, NewNotFoundResult())
				return
			}

			c.Set("bcmJobID", bcmJobID)
			// 后续可以通过 c.MustGet("bcmJob").(*cpromv1.BCMJob) 获取
			c.Set("bcmJob", &m)
		}
	}
}

// ValidateRecordingRule 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = recordingRuleID 可以来自 path 参数，也可以来自 query 参数
func ValidateRecordingRule(k8sClient client.Client, defaultNamespace string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx             = NewContext(c)
			accountID       = c.MustGet("accountID").(string)
			instanceID      = c.MustGet("instanceID").(string)
			recordingRuleID = c.Query("recordingRuleID")
			res             = NewResult()
		)

		if recordingRuleID == "" {
			recordingRuleID = c.Param("ID")
		}

		// todo： this logic will be removed finally
		manageAS := vm_manage.TAccountManage{}
		err := manageAS.Get(nil, instanceID)
		if err != nil {
			logger.Errorf(ctx, "[TAccountManage] to DB failed. errorInfo=[%v]", err.Error())
			res.Message = fmt.Sprintf("get data from manage acount DB failed.")
			c.JSON(http.StatusInternalServerError, res)
			return
		}

		/*
			if len(manageAS.FNewInstanceId) < 1 {
				nn := types.NamespacedName{Name: recordingRuleID, Namespace: instanceID}
				var m vmv1.VMRule
				if err := k8sClient.Get(ctx, nn, &m); err != nil {
					if apierrors.IsNotFound(err) {
						res.Message = fmt.Sprintf("not found recordingRule: %v", nn)
						c.JSON(http.StatusNotFound, res)
						c.Abort()
						return
					}
					res.Message = fmt.Sprintf("check recordingRule: %v, err: %v", nn, err)
					c.JSON(http.StatusInternalServerError, res)
					c.Abort()
					return
				}
				// 这里只确认 recordingRule 是 accountID owner
				if m.Labels[cpromv1.BCEAccountIDLabel] != accountID {
					c.AbortWithStatusJSON(http.StatusForbidden, NewForbiddenResult())
					return
				}
				c.Set("recordingRuleInstance", &m)
			}
		*/

		// 2、检查cprom-alert下是否有该recordingRule
		mann := types.NamespacedName{Name: recordingRuleID, Namespace: defaultNamespace}
		var mam vmv1.VMRule
		if err := k8sClient.Get(ctx, mann, &mam); err != nil {
			if apierrors.IsNotFound(err) {
				res.Message = fmt.Sprintf("not found recordingRule: %v", mann)
				c.JSON(http.StatusNotFound, res)
				c.Abort()
				return
			}
			res.Message = fmt.Sprintf("check recordingRule: %v, err: %v", mann, err)
			c.JSON(http.StatusInternalServerError, res)
			c.Abort()
			return
		}
		// 这里只确认 recordingRule 是 accountID owner
		if mam.Labels[cpromv1.BCEAccountIDLabel] != accountID {
			c.AbortWithStatusJSON(http.StatusForbidden, NewForbiddenResult())
			return
		}
		c.Set("recordingRule", &mam)

		// 3、从mysql中检查instance下是否有该alertingRule
		recordingRuleRecord, err := recording_rule.QueryById(recordingRuleID, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("not found recordingRule. "+
				"instanceID=[%v], recordingRuleID=[%v]", instanceID, recordingRuleID)
			c.JSON(http.StatusNotFound, res)
			c.Abort()
			return
		}
		// 设置recordingRule给下一个middleware 后续可以通过 c.MustGet("recordingRule").(*vmv1.VMRule) 获取
		c.Set("recordingRuleRecord", &recordingRuleRecord)

		c.Set("recordingRuleID", recordingRuleID)

	}
}

func ValidateVMRule() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			accountID  = c.MustGet("accountID").(string)
			instanceID = c.MustGet("instanceID").(string)
			res        = errorcodev2.NewResponse()
			ctx        = NewContext(c)
			m          = vmv1.VMRule{}
		)
		requestID := logger.GetRequestID(ctx)
		logger.Infof(ctx, "validate VMRule. accountID: %v, instanceID: %v.", accountID, instanceID)

		if err := c.Bind(&m); err != nil {
			res.Message = fmt.Sprintf("parse request body err: %v", err)
			logger.Errorf(ctx, "%v", res.Message)
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}

		if len(m.Spec.Groups) == 0 {
			res.Message = "recording rule groups must not be nil."
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}

		if len(m.Spec.Groups) > 1 {
			res.Message = "the number of groups cannot exceed 1."
			c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}

		logger.Infof(ctx, "validate VMRule. m: %+v", m)

		for _, group := range m.Spec.Groups {
			if group.Name == "" {
				res.Message = fmt.Sprintf("group name must not be nil.")
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}

			if len(group.Rules) == 0 {
				res.Message = fmt.Sprintf("rules of group must not be nil.")
				c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
				return
			}
			for _, rule := range group.Rules {
				// 校验每个rule是否是recording rule
				if rule.Record == "" {
					res.Message = fmt.Sprintf("input rule is not a valide recording rule, rule: %v", rule)
					c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
					return
				}

				// validate expr
				_, err := metricsql.Parse(rule.Expr.String())
				if err != nil {
					res.Message = fmt.Sprintf("Invalid MetricsQL query: %v, err: %v", rule.Expr.String(), err)
					c.JSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
					return
				}
			}
		}
		c.Set("vmrule", m)
		return
	}
}

// ValidateAlertingRule 校验 instance owner 该 middlew 必须放在 ValidateInstance 之后
// ID = alertingRuleID 可以来自 path 参数，也可以来自 query 参数
func ValidateAlertingRule(k8sClient client.Client, defaultNamespace string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx            = NewContext(c)
			accountID      = c.MustGet("accountID").(string)
			instanceID     = c.MustGet("instanceID").(string)
			alertingRuleID = c.Query("alertingRuleID")
			res            = NewResult()
		)

		if alertingRuleID == "" {
			alertingRuleID = c.Param("ID")
		}

		// todo： this logic will be removed finally
		manageAS := vm_manage.TAccountManage{}
		err := manageAS.Get(nil, instanceID)
		if err != nil {
			logger.Errorf(ctx, "[TAccountManage] to DB failed. errorInfo=[%v]", err.Error())
			res.Message = fmt.Sprintf("get data from manage acount DB failed.")
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			return
		}
		/*
			if len(manageAS.FNewInstanceId) < 1 {
				nn := types.NamespacedName{Name: alertingRuleID, Namespace: instanceID}
				var m vmv1.VMRule
				if err := k8sClient.Get(ctx, nn, &m); err != nil {
					if apierrors.IsNotFound(err) {
						res.Message = fmt.Sprintf("not found alertingRule: %v", nn)
						c.JSON(http.StatusNotFound, res)
						c.Abort()
						return
					}
					res.Message = fmt.Sprintf("check alertingRule: %v, err: %v", nn, err)
					c.JSON(http.StatusInternalServerError, res)
					c.Abort()
					return
				}
				// 这里只确认 alertingRule 是 accountID owner
				if m.Labels[cpromv1.BCEAccountIDLabel] != accountID {
					c.AbortWithStatusJSON(http.StatusForbidden, NewForbiddenResult())
					return
				}
				c.Set("alertingRuleInstance", &m)
			}
		*/

		// 2、检查cprom-alert下是否有该alertingRule
		mann := types.NamespacedName{Name: alertingRuleID, Namespace: defaultNamespace}
		var mam vmv1.VMRule
		if err := k8sClient.Get(ctx, mann, &mam); err != nil {
			if apierrors.IsNotFound(err) {
				res.Message = fmt.Sprintf("not found alertingRule: %v", mann)
				c.JSON(http.StatusNotFound, res)
				c.Abort()
				return
			}
			res.Message = fmt.Sprintf("check alertingRule: %v, err: %v", mann, err)
			c.JSON(http.StatusInternalServerError, res)
			c.Abort()
			return
		}
		// 这里只确认 alertingRule 是 accountID owner
		if mam.Labels[cpromv1.BCEAccountIDLabel] != accountID {
			c.AbortWithStatusJSON(http.StatusForbidden, NewForbiddenResult())
			return
		}
		c.Set("alertingRule", &mam)

		// 3、从mysql中检查instance下是否有该alertingRule
		alertingRuleRecord, err := alerting_rule.QueryById(alertingRuleID, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("not found alertingRule. "+
				"instanceID=[%v], alertingRuleID=[%v]", instanceID, alertingRuleID)
			c.JSON(http.StatusNotFound, res)
			c.Abort()
			return
		}
		c.Set("alertingRuleRecord", &alertingRuleRecord)

		c.Set("alertingRuleID", alertingRuleID)
		// 设置alertingRule给下一个middleware 后续可以通过 c.MustGet("alertingRule").(*vmv1.VMRule) 获取
	}
}

// ValidateAlertingRuleV2 验证请求的 AlertingRule 是否合法, openapiV2版本
func ValidateAlertingRuleV2(k8sClient client.Client, defaultNamespace string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx            = NewContext(c)
			accountID      = c.MustGet("accountID").(string)
			instanceID     = c.MustGet("instanceId").(string)
			alertingRuleID = c.Query("alertingRuleID")
			res            = errorcodev2.NewResponse()
		)

		requestID := logger.GetRequestID(ctx)
		if alertingRuleID == "" {
			alertingRuleID = c.Param("ID")
		}

		// todo： this logic will be removed finally
		manageAS := vm_manage.TAccountManage{}
		err := manageAS.Get(nil, instanceID)
		if err != nil {
			logger.Errorf(ctx, "[TAccountManage] to DB failed. errorInfo=[%v]", err.Error())
			res.Message = fmt.Sprintf("get data from manage acount DB failed.")
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
			return
		}
		/*
			if len(manageAS.FNewInstanceId) < 1 {
				nn := types.NamespacedName{Name: alertingRuleID, Namespace: instanceID}
				var m vmv1.VMRule
				if err := k8sClient.Get(ctx, nn, &m); err != nil {
					if apierrors.IsNotFound(err) {
						res.Message = fmt.Sprintf("not found alertingRule: %v", nn)
						c.JSON(http.StatusNotFound, res)
						c.Abort()
						return
					}
					res.Message = fmt.Sprintf("check alertingRule: %v, err: %v", nn, err)
					c.JSON(http.StatusInternalServerError, res)
					c.Abort()
					return
				}
				// 这里只确认 alertingRule 是 accountID owner
				if m.Labels[cpromv1.BCEAccountIDLabel] != accountID {
					c.AbortWithStatusJSON(http.StatusForbidden, NewForbiddenResult())
					return
				}
				c.Set("alertingRuleInstance", &m)
			}
		*/

		// 2、检查cprom-alert下是否有该alertingRule
		mann := types.NamespacedName{Name: alertingRuleID, Namespace: defaultNamespace}
		var mam vmv1.VMRule
		if err := k8sClient.Get(ctx, mann, &mam); err != nil {
			if apierrors.IsNotFound(err) {
				res.Message = fmt.Sprintf("not found alertingRule: %v", mann)
				c.JSON(http.StatusNotFound, errorcodev2.NotFoundErrorWithMessage(requestID, res.Message))
				c.Abort()
				return
			}
			res.Message = fmt.Sprintf("check alertingRule: %v, err: %v", mann, err)
			c.JSON(http.StatusInternalServerError, errorcodev2.InternalServerErrorWithMessage(requestID, res.Message))
			c.Abort()
			return
		}
		// 这里只确认 alertingRule 是 accountID owner
		if mam.Labels[cpromv1.BCEAccountIDLabel] != accountID {
			c.AbortWithStatusJSON(http.StatusForbidden, errorcodev2.AccessDeniedError(requestID))
			return
		}
		c.Set("alertingRule", &mam)

		// 3、从mysql中检查instance下是否有该alertingRule
		alertingRuleRecord, err := alerting_rule.QueryById(alertingRuleID, instanceID)
		if err != nil {
			res.Message = fmt.Sprintf("not found alertingRule. "+
				"instanceID=[%v], alertingRuleID=[%v]", instanceID, alertingRuleID)
			c.JSON(http.StatusNotFound, res)
			c.Abort()
			return
		}
		c.Set("alertingRuleRecord", &alertingRuleRecord)

		c.Set("alertingRuleID", alertingRuleID)
		// 设置alertingRule给下一个middleware 后续可以通过 c.MustGet("alertingRule").(*vmv1.VMRule) 获取
	}
}

// QuotaGet quota check
func QuotaGet(cli usersetting.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := NewContext(c)
		accountID := c.MustGet("accountID").(string)
		quota, err := cli.GetUserQuotas(ctx, &usersetting.QuotaRequest{
			UserType:   usersetting.UserTypeAccountID,
			UserValue:  accountID,
			QuotaTypes: []usersetting.QuotaType{"instanceQuota"},
		}, usersetting.NewSignOption())

		if err != nil {
			errorMsg := fmt.Sprintf("get user quota failed: %v", err)
			logger.Errorf(ctx, errorMsg)
			c.AbortWithStatusJSON(http.StatusInternalServerError, NewFailedResult(errorMsg))
			return
		}

		// default instanceQuota = 5
		quotaNum := 5
		insQuota := quota.QuotaType2Quota["instanceQuota"]
		logger.Infof(ctx, "accountId: %s, quota: %v", accountID, quota)

		if insQuota != "" {
			quotaNum, err = strconv.Atoi(insQuota)
			if err != nil {
				errorMsg := fmt.Sprintf("convert quota to int failed: %v", err)
				logger.Errorf(ctx, errorMsg)
			}
		}
		c.Set("quota", quotaNum)
	}
}

// CheckUserQuota check user quota
func CheckUserQuota(k8sClient client.Client, accountManage vm_manage.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := NewContext(c)
		accountID := c.MustGet("accountID").(string)
		quotaNum := c.MustGet("quota").(int)

		// select soft instance
		inUsed, err := accountManage.QueryByAccountIdAndAvailable(nil, accountID)
		if err != nil {
			errorMsg := fmt.Sprintf("query account manage failed: %v", err)
			logger.Errorf(ctx, errorMsg)
			c.AbortWithStatusJSON(http.StatusInternalServerError, errorcodev2.InternalServerError(logger.GetRequestID(ctx)))
			return
		}
		logger.Infof(ctx, "list monitor instance len: %v from db", len(inUsed))

		// select real instance
		var mm cpromv1.MonitorInstanceList
		labelSelector, err := labels.Parse(fmt.Sprintf("%s=%s", cpromv1.BCEAccountIDLabel, accountID))
		if err != nil {
			errorMsg := fmt.Sprintf("parse labelSelector err: %v", err)
			logger.Errorf(ctx, errorMsg)
			c.AbortWithStatusJSON(http.StatusInternalServerError, NewFailedResult(errorMsg))
			return
		}

		if err := k8sClient.List(ctx, &mm, &client.ListOptions{
			LabelSelector: labelSelector,
		}); err != nil {
			errorMsg := fmt.Sprintf("list %s err: %v", labelSelector, err)
			logger.Errorf(ctx, errorMsg)
			c.AbortWithStatusJSON(http.StatusInternalServerError, NewFailedResult(errorMsg))
			return
		}
		logger.Infof(ctx, "list monitor instance len: %v from k8s", len(mm.Items))

		if len(mm.Items)+len(inUsed) >= quotaNum {
			errorMsg := fmt.Sprintf("The number of instances had reached the maximum limit of %v for the instance quota.", quotaNum)
			logger.Errorf(ctx, errorMsg)
			c.AbortWithStatusJSON(http.StatusForbidden, NewFailedResult(errorMsg))
			return
		}

	}
}

func WhitelistAccess(cli usersetting.Interface, region string, saveForNext bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := NewContext(c)
		accountID := c.MustGet("accountID").(string)

		// gztest 没有白名单，与gz保持一致
		if region == "gztest" {
			region = "gz"
		}
		if region == "bjtest" {
			region = "bj"
		}

		if region == "bdtest" {
			region = "bd"
		}

		resp, err := cli.CheckWhiteList(ctx, &usersetting.FeatureAclRequest{
			FeatureType: EnableCprom,
			AclType:     "AccountId",
			Region:      region,
			AclName:     accountID,
		}, usersetting.NewSignOption())

		if err != nil {
			logger.Errorf(ctx, "check whitelist failed: %s", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, NewFailedResult("access whitelist failed"))
			return
		}

		if !resp.IsExist && !saveForNext {
			logger.Errorf(ctx, "user %s is not in whitelist", accountID)
			c.AbortWithStatusJSON(http.StatusForbidden, NewFailedResult("not in whitelist"))
			return
		}

		c.Set("inWhitelist", resp.IsExist)
	}
}

func ProcessAccountID() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := NewContext(c)
		accountIDForBackend, isExist := c.Get("accountIDBackend") // 若有accountIDBackend，则用accountIDBackend替换从ak、sk中解析得到的accountID，专门用于给后端访问
		if isExist {
			c.Set("accountID", accountIDForBackend)
			logger.Infof(ctx, "replace accountID from param of path. accountID: %v, path: %v, method: %v",
				accountIDForBackend, c.Request.URL, c.Request.Method)
		}
	}
}

// 定义校验的middleware
func InvalidMonitorGrafana(k8sClient client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			ctx              = NewContext(c)
			accountId        = c.MustGet("accountID").(string)
			monitorGrafanaId = c.Param("ID")
			res              = errorcodev2.NewResponse()
		)

		requestID := logger.GetRequestID(ctx)
		// 如果monitorGrafanaId为空，直接返回错误
		if len(monitorGrafanaId) == 0 {
			res.Message = fmt.Sprintf("no Grafana id, %s", monitorGrafanaId)
			logger.Errorf(ctx, res.Message)
			c.AbortWithStatusJSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
		var monitorGrafana cpromv1.MonitorGrafana
		// 查询k8s 获取对应Grafana的crd
		if err := k8sClient.Get(ctx, types.NamespacedName{
			Namespace: "cprom-grafana",
			Name:      monitorGrafanaId,
		}, &monitorGrafana); err != nil {
			res.Message = fmt.Sprintf("get k8s fail, name = %s, err = %v", monitorGrafanaId, err)
			logger.Errorf(ctx, res.Message)
			c.AbortWithStatusJSON(http.StatusNotFound, errorcodev2.NotFoundError(requestID))
			return
		}

		// 对比此时修改的grafana是否为本资源账号下的
		crdAccountId, ok := monitorGrafana.Labels[cpromv1.BCEAccountIDLabel]
		if !ok {
			res.Message = fmt.Sprintf("grafana no accountId")
			logger.Errorf(ctx, res.Message)
			c.AbortWithStatusJSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
		if crdAccountId != accountId {
			res.Message = fmt.Sprintf("forbid change this grafana, accountId is not same")
			logger.Errorf(ctx, res.Message)
			c.AbortWithStatusJSON(http.StatusBadRequest, errorcodev2.BadRequestWithMessage(requestID, res.Message))
			return
		}
		// 填写ginContext
		c.Set("monitor-grafana", &monitorGrafana)
		c.Set("monitor-grafana-id", monitorGrafanaId)
	}
}

// 移除agentID后缀
func RemoveAgentID(input string) string {
	if strings.Contains(input, "-agent-") {
		result := input[:len(input)-16]
		return result
	}
	return input
}
